# retro_synthesis_mcts/api/retro_api.py
import pandas as pd
import numpy as np
import joblib
import pickle
import xgboost as xgb
from sklearn.preprocessing import StandardScaler

from api.reaction_classification_api import *
from api.trasformers_api import *
from core.single_step_retro.disconnections_relaxed import *
from infra.db.disconnections_db import *
from infra.db.disconnections_fileStorage import *
from utils.ltr_features import *

class SingleStepRetroRelaxedAPI:
    """API client for single-step retrosynthesis predictions."""
    
    def __init__(self,transformer_AutoTag,transformer_T1,transformer_T2, transformer_T3):
        """
        Initialize the retrosynthesis API.
        """    
        list_substructures_path= 'assets/stocks/list_conditionnal_substructures_tags_R2.csv'
        if list_substructures_path != '':
            with open(list_substructures_path, 'r') as f:
                reader = csv.reader(f)
                substructure_list = list(reader)   

        self.disconnection_tool = TopDisconnectionsRelaxed(
            transformer_AutoTag,
            transformer_T1,
            transformer_T2, 
            transformer_T3,                   
            uspto_t1_path="assets/mttl_models/USPTO_STEREO_separated_T1_Retro_255000.pt",
            uspto_t2_path="assets/mttl_models/USPTO_STEREO_separated_T2_Reagent_Pred_225000.pt",
            uspto_t3_path="assets/mttl_models/USPTO_STEREO_separated_T3_Forward_255000.pt",
            autotag_model_path="assets/mttl_models/USPTO_STEREO_separated_T0_AutoTag_260000.pt",
            substructure_list=substructure_list # Example substructures
        )  
        ltr_model_path = "assets/ltr/minimal_ltr_1_8_3.joblib"
        components = joblib.load(ltr_model_path)
        self.ltr_model = components['model']
        self.ltr_scaler = components['scaler']
        self.feature_columns = components['feature_columns']
    
    def get_retrosynthesis_reactions(self, target_smiles):
        """
        Get possible retrosynthesis reactions for a target molecule.
        
        Args:
            target_smiles (str): SMILES of the target molecule
            
        Returns:
            list: List of Reaction objects
        """
        reactions = []
        target_predictions = pd.DataFrame() 

        check_disconnections_db = 0
        if check_disconnections_db ==1:
            target_predictions = query_disconnections_target(self.canonicalize_smiles(target_smiles))
            target_predictions.columns=['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']#, 'rxn_string', 'rxn_class']
            print(f"Loaded {len(target_predictions)} records from db")   

        check_disconnections_fileStorage = 1
        if check_disconnections_fileStorage ==1:
            storage = DisconnectionsStorage()
            target_predictions = storage.load_disconnections(self.canonicalize_smiles(target_smiles)) 
            if 'index' in target_predictions.columns:
                target_predictions['index'] = target_predictions.index
            print(f"Loaded {len(target_predictions)} records from fileStorage")  
            if len(target_predictions) >0:
                print(target_predictions.shape)
                #print(target_predictions.iloc[0])
                target_predictions= target_predictions[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf']]
                target_predictions['index'] = target_predictions.index
                #target_predictions.columns=['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']#, 'rxn_string', 'rxn_class']
        
        #exit(1)
        if len(target_predictions) ==0 :
            print("No disconnections found in DB - creating from scratch")
            # Filter predictions for the target molecule
            target_predictions = self.disconnection_tool.get_potential_disconnections(target_smiles)    
            if target_predictions.empty:
                return reactions
            print("*********")
            print("+++++++")
            print(f"Created {target_predictions.shape} disconnections from scratch")
            print(target_predictions.iloc[0]) 
            #exit(1)        
            insert_disconnections_db = 0
            if insert_disconnections_db ==1:        
                success = insert_dataframe_to_postgres(target_predictions)
                if success:
                    print("Data inserted successfully!")
                else:
                    print("Failed to insert data")  

            insert_disconnections_fileStorage = 1
            if insert_disconnections_fileStorage ==1:  
                storage = DisconnectionsStorage()
                target_predictions_temp= target_predictions[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf']]
                    
                success = storage.save_disconnections(target_predictions_temp, self.canonicalize_smiles(target_smiles), analysis_id='test_analysis')
                print(f"Save successful: {success}")
                #exit(1)
                if success:
                    print("Data inserted successfully!")
                else:
                    print("Failed to insert data")                      
    
        else:
            print(f"Found {len(target_predictions)} disconnections in DB: returning")
        
        # 1. Cut off based RetroConf Score
        # Rank-based percentile approach:
        # Top 20% (>= 0.005323): 3042 rows
        # Top 10% (>= 0.022504): 1521 rows
        # Top 5% (>= 0.078865): 761 rows
        # Top 1% (>= 0.512325): 153 rows
        print(target_predictions.shape)

        target_predictions = target_predictions[target_predictions['Retro_Conf'] > 0.005]
    
        target_predictions.drop_duplicates(subset=['Retro'], keep='first', inplace=True)
        target_predictions = target_predictions[target_predictions['Target'] != target_predictions['Retro']]
       
        ## Add LTR Score here
        print(target_predictions.shape)
        #exit(1)
        target_predictions = enhance_dataframe_with_scores(target_predictions)
        print(target_predictions.columns)
        #exit(1)
        target_predictions = create_derived_features(target_predictions)
        print(target_predictions.columns)
        # Scale the features and get score
        # Ensure all feature columns exist
        for col in self.feature_columns:
            if col not in target_predictions.columns:
                target_predictions[col] = 0
        
        # Extract features
        X = target_predictions[self.feature_columns].values
        # Scale features
        X_scaled = self.ltr_scaler.transform(X)
        # Make predictions
        predictions = self.ltr_model.predict(X_scaled)
        # Add to df
        target_predictions['ltr_score'] = predictions
        print(target_predictions.shape)
        #exit(1)

        # 2. Call T2
        target_predictions = self.disconnection_tool.retro_model.call_T2(target_predictions)
        print(target_predictions.shape)
        #exit(1)
        #remove invalid discconections by roundtrip logid
        # Keep predictions where T3 predicts the correct target, and target is not in the retro prediction:
        # print(target_predictions.shape)
        # target_predictions_Forw_val = target_predictions[target_predictions['Target'] == target_predictions['Forward_Prediction']]
        # print(target_predictions_Forw_val.shape)
        
        # print(target_predictions_Forw_val.shape)        

        target_predictions['rxn_string'] =target_predictions.apply(lambda s: s['Retro'] + '>' + s['Reagents'] + '>' + target_smiles,axis=1)
        target_predictions['rxn_class'] = target_predictions.apply(lambda s: get_reaction_class(s['rxn_string']),axis=1)
        # print(target_predictions_Forw_val.shape) 
        # print(target_predictions_Forw_val.iloc[0])

        #exit(1)
        return target_predictions

    def canonicalize_smiles(self, smiles: str) -> str:
        '''
        Molecule canonicalization that does not change the SMILES order of molecules in case of multiple molecules.
        Also neutralizes any charge of the molecules.
        
        Args:
            smiles (str): SMILES string of the molecule(s).
        
        Returns:
            str: Canonicalized SMILES string of the molecule(s).
        '''
        returned = []
        any_error = False
        for molecule in smiles.split('.'):
            molecule = self.neutralize_smi(molecule)
            mol = Chem.MolFromSmiles(molecule)
            if mol is not None:
                returned.append(Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True))
            else: 
                any_error = True
        if not any_error:
            return '.'.join(returned)
        else:
            return ''
        
    def neutralize_smi(self, smiles: str) -> str:        # from: https://www.rdkit.org/docs/Cookbook.html#neutralizing-molecules
        if '-' in smiles or '+' in smiles:
            try:
                mol = Chem.MolFromSmiles(smiles)
                pattern = Chem.MolFromSmarts("[+1!h0!$([*]~[-1,-2,-3,-4]),-1!$([*]~[+1,+2,+3,+4])]")
                at_matches = mol.GetSubstructMatches(pattern)
                at_matches_list = [y[0] for y in at_matches]
                if len(at_matches_list) > 0:
                    for at_idx in at_matches_list:
                        atom = mol.GetAtomWithIdx(at_idx)
                        chg = atom.GetFormalCharge()
                        hcount = atom.GetTotalNumHs()
                        atom.SetFormalCharge(0)
                        atom.SetNumExplicitHs(hcount - chg)
                        atom.UpdatePropertyCache()
                return Chem.MolToSmiles(mol)
            except:
                return smiles
        else:
            return smiles
