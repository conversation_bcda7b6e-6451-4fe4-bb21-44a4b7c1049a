
from enum import Enum
import logging
import uuid
import hashlib
from pymongo import MongoClient
from datetime import datetime
import os

MONGO_URL = os.getenv('MONGO_URL', 'mongodb://root:<EMAIL>:27017')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
class RetroStatus(Enum):
    """
    Enum to represent the status of the retro synthesis process.
    """
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class DbOps:
    """
    A class to handle MongoDB operations for logging retro synthesis requests.
    """
    def __init__(self):
        self.client = MongoClient(MONGO_URL)
        self.db = self.client['retro_synthesis']
        self.requests_collection = self.db['retro_requests']
        # self._create_indexes()

    def _create_indexes(self):
        """
        Create necessary indexes for the collections.
        """
        self.requests_collection.create_index('request_id', unique=True)

    def insert_log(self, request_id, input_type, input_value, status, user_id='', tenant_id='', error_message=None):
        """
        Insert or update a log entry and track status history.
        """
        current_time = datetime.utcnow()
        
        # Prepare the document for new entries
        log_doc = {
            '_id' : str(uuid.uuid4()),
            'request_id': request_id,
            'input_type': input_type,
            'input_value': input_value,
            'status': status.value,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'error_message': error_message,
            'created_at': current_time,
            'updated_at': current_time
        }

        # Update status and updated_at for existing documents, or insert new document
        self.requests_collection.update_one(
            {'request_id': request_id},
            {
            '$set': {
                'status': status.value,
                'updated_at': current_time
            },
            '$setOnInsert': {k: v for k, v in log_doc.items() if k not in ['status', 'updated_at']}
            },
            upsert=True
        )

        logger.info(f"Updated log for request_id: {request_id}, status: {status.value}")
    
    def insert_retro_data(self, request_id, unique_id, route_id, num_steps, total_route_score, target_smiles, total_cost ,raw_smiles, route_level_image, route_name, data):
        """
        Insert retro synthesis data into the database.
        """
        logger.info(f"Inserting retro data for request_id: {request_id}, unique_id: {unique_id}, route_id: {route_id}")
        current_time = datetime.now()
        data_doc = {
            '_id' : str(uuid.uuid4()),
            'request_id': request_id,
            'route_id': route_id,
            'unique_id': unique_id,
            'target_smiles': target_smiles,
            'route_name': route_name,
            'route_reaction_img': route_level_image,
            'raw_reactants': raw_smiles,
            'num_steps': num_steps,
            'total_route_score': total_route_score,
            'total_cost': total_cost,
            'data': data,
            'retro_status': 'IN_PROGRESS',
            'created_at': current_time,
            'updated_at': current_time
        }
        try:
            # Update if unique_id exists, otherwise insert new document
            result = self.db['retro_data'].update_one(
                {'unique_id': unique_id},
                {
                    '$set': {
                        'updated_at': current_time
                    },
                    '$setOnInsert': {k: v for k, v in data_doc.items() if k != 'updated_at'}
                },
                upsert=True
            )
            if result.upserted_id:
                logger.info(f"Inserted new retro data for unique_id: {unique_id}")
            else:
                logger.info(f"Updated existing retro data for unique_id: {unique_id}")
        except Exception as e:
            logger.error(f"Failed to upsert retro data for unique_id {unique_id}: {str(e)}")
    
    def get_top_routes(self, request_id, limit=100):
        """
        Retrieve the top routes for a given target SMILES.
        
        Parameters:
        ----------
        target_smiles : str
            The target SMILES string to search for.
        limit : int
            The maximum number of routes to return.
        
        Returns:
        -------
        list
            A list of dictionaries containing the top routes.
        
        """
        query = {
            'request_id': request_id,
        }
        projection = {
            '_id': 1,
            'route_id': 1,
            'num_steps': 1,
            'total_route_score': 1,
            'data': 1
        }
        
        routes = list(self.db['retro_data'].find(query, projection).sort('total_route_score', -1).limit(limit))
        return routes
    
    def update_retro_data(self, id, data):
        """
        Update retro synthesis data in the database.
        
        Parameters:
        ----------
        id : str
            The request ID for which to update the data.
        data : dict
            The data to update in the database.
        
        """
        current_time = datetime.now()
        
        result = self.db['retro_data'].update_one(
            {'_id': id},
            {'$set': {'data': data, 'updated_at': current_time , 'retro_status' : 'COMPLETED'}}
        )

        
        if result.modified_count > 0:
            logger.info(f"Updated retro data for id: {id}")
        else:
            logger.warning(f"No updates made for id: {id}")

    def close(self):
        """
        Close the MongoDB connection.
        
        """
        self.client.close()
