
from api.singleStepRetro_api import *
from api.synthesis_score_api import *
from api.singleStepRetroRelaxed_api import *
from config.settings import Config
from treeSearchEngine import *
from db.db_ops import DbOps, RetroStatus
from celery.utils.log import get_task_logger

db_ops = DbOps()
logger = get_task_logger("tasks")

def retro_runner(target_smiles, request_id=None, kwargs : dict = {}):
    """Example of how to use the TreeSearchEngine."""
    
    #Assuming you have your SingleStepRetroAPI instance
    pre_loaded_retro_models = 0
    if pre_loaded_retro_models ==1:
        model_path = "assets/mttl_models/USPTO_STEREO_separated_T0_AutoTag_260000.pt"
        AutoTagging_Beam_Size=50
        transformer_AutoTag = load_onmt_transformer(model_path,beam_size=AutoTagging_Beam_Size)
        
        Retro_beam_size=3
        model_path = "assets/mttl_models/USPTO_STEREO_separated_T1_Retro_255000.pt"
        transformer_T1 = load_onmt_transformer(model_path,beam_size=Retro_beam_size)

        USPTO_Reag_Beam_Size=3
        model_path = "assets/mttl_models/USPTO_STEREO_separated_T2_Reagent_Pred_225000.pt"
        transformer_T2 = load_onmt_transformer(model_path,beam_size=USPTO_Reag_Beam_Size)

        model_path = "assets/mttl_models/USPTO_STEREO_separated_T3_Forward_255000.pt"
        transformer_T3 = load_onmt_transformer(model_path,beam_size=3)    
    else:
        transformer_AutoTag = None
        transformer_T1 = None
        transformer_T2 = None
        transformer_T3 = None    

    single_step_api = SingleStepRetroRelaxedAPI(transformer_AutoTag,transformer_T1,transformer_T2, transformer_T3)
    synthesis_score_api=SCScoreAPI()
    settings = Config()
    
    #Create the tree search engine
    engine = TreeSearchEngine(single_step_api,synthesis_score_api,settings, kwargs)
    
    #Find synthesis routes
    print(target_smiles)
    routes = engine.find_synthesis_routes(target_smiles=target_smiles, max_routes=400, kwargs=kwargs)
    # print(routes)
    # #Print results
    # for i, route in enumerate(routes):
    #     print(f"Route {i+1}:")
    #     for reaction in route:
    #         print(f"  Reaction: {reaction.reaction_data.get('rxn_string', 'N/A')}")
    #     print() 

    # Store pathways for later analysis
    logger.info(f"Storing pathways for target SMILES: {target_smiles}")
    pathways = engine.store_pathways(target_smiles=target_smiles, routes=routes , kwargs=kwargs)

    # for dt  in pathways.get('routes', []):
    #     if dt:
    #         db_ops.insert_retro_data(
    #             request_id=request_id,
    #             route_id=dt.get('route_id', 0),
    #             num_steps=dt.get('num_steps', 0),
    #             total_route_score=dt.get('total_route_score', 0.0),
    #             data=dt.get('reactions', []),
    #         )
    

    logger.info(f"Completed retro synthesis for target SMILES: {target_smiles}")
    
    return len(routes)  # Return the number of routes found
    
    #pass

# if __name__ == "__main__":
#     chem_loop = {
#         "2-Amino-5-chloro-3-methyl-benzoic-acid": "O=C(O)C1=CC(Cl)=CC(=C1N)C",        
#         # "2-Amino-5-chloro-N-3-dimethylbenzamide" : "O=C(NC)C1=CC(Cl)=CC(=C1N)C",
#         # "2-Amino-5-cyano-3-methyl-benzoic-acid" : "N#CC=1C=C(C(=O)O)C(N)=C(C1)C",
#         # "2-Amino-5-cyano-N-3-dimethylbenzamide" : "N#CC=1C=C(C(=O)NC)C(N)=C(C1)C",
#         # "4-Heptafluoroisopropyl-2-methylaniline" : "FC(F)(F)C(F)(C1=CC=C(N)C(=C1)C)C(F)(F)F",
#         # "2-chloro-4-methylsulfonyl-3-2-2-2-trifluoroethoxy-methyl-benzoic-acid" : "O=C(O)C1=CC=C(C(=C1Cl)COCC(F)(F)F)S(=O)(=O)C",
#         # #"3-4-5-Trifluorobiphenyl-2-Amine" : "FC1=CC(=CC(F)=C1F)C=2C=CC=CC2N",
#         # "Tetrahydrofuran-3-yl-methanamine" : "O1CCC(C1)CN",
#         # "3-Methyl-4-(trifluoromethyl)aniline": "NC1=CC=C(C(F)(F)F)C(C)=C1",
#         # #"N Vinyl Formamide" : "O=CNC=C",
#         # #"2-Fluoro 6-chloro Benzaldehyde" : "O=CC=1C(F)=CC=CC1Cl",
#         # #"1-Chloro-4-fluorobenzene" : "FC1=CC=C(Cl)C=C1",
#         # #"methyl 3-bromo-2-fluoro-4-methylbenzoate" : 	"O=C(OC)C1=CC=C(C)C(Br)=C1F",
#         # "4-Butylresorcinol": "OC1=CC=C(C(O)=C1)CCCC" ,

#         # #NEW TEST MOLECULES
#         # "3-(trifluoromethyl)pyridine-4-carboxamide":"O=C(N)c1cnc(C(F)(F)F)cc1",
#         # "4-(trifluoromethoxy)aniline":"Nc1ccc(OC(F)(F)F)cc1",
#         # "2,5-Dichloro-4-fluoro-1-nitrobenzene":"O=[N+]([O-])c1c(Cl)cc(F)c(Cl)c1",
#         # "2-Chloro-5-methylbenzaldehyde":"Cc1ccc(Cl)c(C=O)c1",
#         # "2,2-Difluoroethyl methyl sulfone": "CS(=O)(=O)CC(F)F"       
#     }    
#     for key in chem_loop.keys():
#         target_smiles=chem_loop[key] #["2-Amino-5-chloro-3-methyl-benzoic-acid"]
#         print(f"target-{key} -- target_smiles={target_smiles}")
#         retro_runner(target_smiles , 'sbbkss')