import requests
import json
import os 

API_HUB_ENDPOINT = os.getenv("API_HUB_ENDPOINT", "http://host.docker.internal:8000")


static_response = {
  "reaction_smiles_interpreted": "O=C(O)c1ccncc1C(F)(F)F.ClS(=O)Cl.ClS(=O)Cl>>O=C(Cl)c1ccncc1C(F)(F)F.O=S.Cl",
  "reaction_details": {
    "reactants_identified": [
      "3-(trifluoromethyl)pyridine-4-carboxylic acid",
      "thionyl dichloride",
      "thionyl dichloride"
    ],
    "products_identified": [
      "3-(trifluoromethyl)pyridine-4-carbonyl chloride",
      "sulfur monoxide",
      "molecular chlorine"
    ],
    "reaction_name": "Carboxylic acid to acid chloride"
  },
  "reagents_and_solvents": [
    {
      "name": "thionyl dichloride",
      "role": "Reagent/Catalyst (LLM Predicted)",
      "price_per_unit": 1100.0,
      "currency": "INR",
      "unit_basis": "kg",
      "price_source": "Secondary Local Data (Local Cache)",
      "price_confidence": "High"
    },
    {
      "name": "dichloromethane",
      "role": "Solvent (LLM Predicted)",
      "price_per_unit": 37.0,
      "currency": "INR",
      "unit_basis": "kg",
      "price_source": "Primary Local Data (Local Cache)",
      "price_confidence": "High"
    },
    {
      "name": "chloroform",
      "role": "Solvent (LLM Predicted)",
      "price_per_unit": 21.0,
      "currency": "INR",
      "unit_basis": "kg",
      "price_source": "Primary Local Data (Local Cache)",
      "price_confidence": "High"
    }
  ],
  "reaction_conditions": {
    "temperature_from_dataset": "Room Temperature to Reflux",
    "time_from_dataset": "2-4 hours",
    "yield_from_dataset": "70-90%",
    "atmosphere_llm_or_dataset": "Nitrogen"
  },
  "safety_and_notes": {
    "safety": "This reaction involves the conversion of a carboxylic acid to an acid chloride using thionyl dichloride, which releases toxic gases such as sulfur dioxide and hydrogen chloride. Proper ventilation and personal protective equipment (PPE) are essential to ensure safety.",
    "notes": "Conduct the reaction in a well-ventilated fume hood to prevent exposure to toxic gases. Use appropriate PPE, including gloves and goggles. Handle thionyl dichloride with care, as it is corrosive and reacts violently with water. Ensure that all glassware is dry and free from moisture to prevent side reactions. Monitor the reaction temperature to avoid excessive heat generation."
  },
  "procedure_steps": [
    "Dissolve 3-(trifluoromethyl)pyridine-4-carboxylic acid in dichloromethane.",
    "Add thionyl chloride dropwise to the solution under a nitrogen atmosphere.",
    "Stir the reaction mixture at room temperature, then heat to reflux if necessary.",
    "Monitor the reaction progress by TLC.",
    "Once complete, remove excess thionyl chloride under reduced pressure.",
    "Quench the reaction with a suitable quenching agent if necessary.",
    "Extract the product with an organic solvent and wash with water.",
    "Dry the organic layer over anhydrous magnesium sulfate.",
    "Concentrate the solution under reduced pressure to obtain the crude product.",
    "Purify the product by recrystallization or column chromatography if needed."
  ]
}

def get_reaction_smiles_data(reaction_smiles):
    """
    Fetches reaction data from the Retro API for a given reaction SMILES string.
    
    Args:
        reaction_smiles (str): The SMILES representation of the reaction.
        
    Returns:
        dict: The JSON response from the Retro API containing reaction data.
    """
    url = f"http://{API_HUB_ENDPOINT}/api/v1/chemcopilot/query"
    headers = {"Content-Type": "application/json", "accept": "application/json"}
    payload  = {
    "query": f"Give the full info about this reaction - {reaction_smiles}",
    }
    payload = json.dumps({
      "query": f"Give the full info about this reaction - {reaction_smiles}",
      "smiles" : reaction_smiles,
        "session_id": "string",
        "original_name_for_saving": "string",
        "clear_moi_before_query": False
      })
    
    try:
        response = requests.post(url, data=payload, headers=headers, timeout=700)
        response.raise_for_status()  # Raise an error for bad responses
        return response.json().get('analysis')
    except requests.RequestException as e:
        print(f"Error fetching data from Retro API: {e}")
        return static_response  # Return static response in case of error
    


def smiles_to_iupac_pubchem(smiles):
    try:
        url = f"http://{API_HUB_ENDPOINT}/api/v1/chemcopilot/chemical_converter"
        data = {
            "identifier": smiles,
        }
        response = requests.post(url, data=json.dumps(data), headers={"Content-Type": "application/json"}, timeout=30)
        if response.ok:
            data = response.json()
            return data.get('iupac_name')
        else:
            return "Failed to fetch IUPAC name"
    except:
        return ''

