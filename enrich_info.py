
import logging
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from db.db_ops import DbOps
from utils.common import get_reaction_smiles_data
from utils.helpers import REQUEST_ID


db_ops = DbOps()


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def enrich_data():
    """Enriches the top 100 routes with additional reaction data.
    This function retrieves the top 100 routes from the database, extracts unique reaction strings,
    fetches their corresponding SMILES data, and updates the routes with this additional information.
    """
    ENRICHED_LIMIT = int(os.getenv('ENRICHED_LIMIT', '100'))
    request_id = os.getenv('REQUEST_ID')
    if not request_id:
        logger.error("Request ID is not set. Cannot proceed with enrichment.")
        return
    top_100_routes = db_ops.get_top_routes(
        request_id=request_id, limit=ENRICHED_LIMIT)
    
    if not top_100_routes:
        logger.info(f"No top routes found for request {request_id}. Skipping enrichment.")
        return
    unique_reaction_list = []
    if top_100_routes:
        logger.info(f"Top 100 routes for request {request_id}:")
        for route in top_100_routes:
            for steps in route.get('data', []):
                if steps.get('reaction_string'):
                    unique_reaction_list.append(steps.get('reaction_string'))
    
    unique_reaction_list = list(set(unique_reaction_list))

    smiles_metadata = {}
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = {
            executor.submit(get_reaction_smiles_data, smiles): smiles
            for smiles in unique_reaction_list
        }

        for future in as_completed(futures):
            smiles = futures[future]
            try:
                reaction_data = future.result()
                smiles_metadata[smiles] = reaction_data
            except Exception as e:
                logger.error(f"Error getting reaction data for {smiles}: {e}")
                smiles_metadata[smiles] = {}
    
    # Save the results to the database
    for route in top_100_routes:
        for step in route.get('data', []):
            rxn_string = step.get('reaction_string', '')  # Match the key used earlier
            step['other_information'] = smiles_metadata.get(rxn_string, {})
        
        db_ops.update_retro_data(route.get('_id', ''), route.get('data', []))