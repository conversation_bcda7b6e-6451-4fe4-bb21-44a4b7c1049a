import os
import json
import logging
import traceback
import time
import socket
import redis
from http import HTT<PERSON>tatus
from http.server import HTTPServer, BaseHTTPRequestHandler
from http.server import ThreadingHTTPServer
from utils.helpers import REQUEST_ID
from config.settings import Config
from enrich_info import enrich_data
from celery.schedules import crontab
from celery import Celery
from celery.signals import after_setup_logger, worker_process_init
from retro_runner import retro_runner
from threading import Thread
from db.db_ops import DbOps, RetroStatus


app_config = Config()
# Logging configuration
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Celery configuration

app = Celery(__name__, broker=app_config.REDIS_URL, backend=app_config.REDIS_URL)
app.conf.update(
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=True,
    task_reject_on_worker_lost=True,
    task_ignore_result=False,
    worker_max_tasks_per_child=1,
    task_default_queue=app_config.RETRO_INPUT_REDIS_QUEUE,  # Add this line
    task_routes={
        'process_retro_task': {'queue': app_config.RETRO_INPUT_REDIS_QUEUE},
    }
)

@after_setup_logger.connect
def setup_celery_logger(logger, *args, **kwargs):
    os.makedirs('logs', exist_ok=True)
    fh = logging.FileHandler('logs/tasks.log')
    fh.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(fh)




# Redis utility to push result
class RedisClient:
    def __init__(self, redis_url):
        self.redis = redis.Redis.from_url(redis_url, decode_responses=True)
        self.redis.ping()

    def insert_into_output_queue(self, data):
        self.redis.rpush(app_config.RETRO_OUTPUT_REDIS_QUEUE, json.dumps(data))

    def get_queue_length(self, queue_name):
        return self.redis.llen(queue_name)

# Health check
class HealthCheckHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            try:
                client = RedisClient(app_config.REDIS_URL)
                data = {
                    "status": "healthy",
                    "output_queue_length": client.get_queue_length(app_config.RETRO_OUTPUT_REDIS_QUEUE),
                    "timestamp": time.time()
                }
            except Exception as e:
                data = {
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": time.time()
                }

            try:
                self.send_response(HTTPStatus.OK)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(data).encode())
            except (BrokenPipeError, ConnectionResetError, socket.error) as e:
                logger.warning(f"[HEALTH] Client disconnected before response could be sent: {e}")

    def log_message(self, format, *args):
        return



def handle_error(e, request_id, target_smiles, molecule_name, self_task, start_time, db_ops : DbOps):
    error_msg = str(e)
    logger.error(f"[ERROR] {request_id}: {error_msg}")
    logger.error(traceback.format_exc())
    
    if self_task.request.retries < self_task.max_retries:
        raise self_task.retry(countdown=60 * (2 ** self_task.request.retries))
    
    db_ops.insert_log(
        request_id=request_id,
        input_type="SMILES",
        input_value=target_smiles,
        status=RetroStatus.FAILED,
        error_message=error_msg
    )
    
    app.send_task(
        'retro_result_handler',
        args=[{
            "request_id": request_id,
            "molecule_name": molecule_name,
            "status": "FAILED",
            "error_message": error_msg,
            "failed_at": time.time(),
            "task_id": self_task.request.id,
            "retries": self_task.request.retries,
            "processing_time": time.time() - start_time
        }],
        queue=app_config.RETRO_OUTPUT_REDIS_QUEUE
    )

def process_single_iteration(itr, request_id, target_smiles, molecule_name, self_task, start_time, db_ops : DbOps):
    if itr.get('MAX_DEPTH'):
        os.environ['MAX_DEPTH'] = str(itr.get('MAX_DEPTH'))
    if itr.get('BEAM_WIDTH'):
        os.environ['BEAM_WIDTH'] = str(itr.get('BEAM_WIDTH'))
    
    logger.info(f"[THREAD] Starting iteration with max_depth={itr.get('MAX_DEPTH', 'N/A')} and beam_width={itr.get('BEAM_WIDTH', 'N/A')} for request_id {request_id}")
    try:
        db_ops.insert_log(
            request_id=request_id,
            input_type="SMILES",
            input_value=target_smiles,
            status=RetroStatus.RUNNING,
        )
        checkpoint_frequency = int(str((itr.get('MAX_DEPTH') + 1) * 100))
        kwargs = {
            "max_depth": int(itr.get('MAX_DEPTH', 5)),
            "beam_width": int(itr.get('BEAM_WIDTH', 3)),
            "checkpoint_frequency": checkpoint_frequency,
            "request_id": request_id,

        }
        logger.info(f"[START] Processing task {request_id} with SMILES: {target_smiles}")
        total_routes = retro_runner(target_smiles, request_id=request_id, kwargs=kwargs)
        processing_time = time.time() - start_time
        
        logger.info(f"[SUCCESS] Completed task {request_id} in {processing_time:.2f}s")
        db_ops.insert_log(
            request_id=request_id,
            input_type="SMILES",
            input_value=target_smiles,
            status=RetroStatus.COMPLETED,
        )
        
        app.send_task(
            'retro_result_handler',
            args=[{
                "request_id": request_id,
                "molecule_name": molecule_name,
                "status": "SUCCESS",
                "total_routes": total_routes,
                "processed_at": time.time(),
                "task_id": self_task.request.id,
                "processing_time": processing_time
            }],
            queue=app_config.RETRO_OUTPUT_REDIS_QUEUE
        )
        logger.info(f"[END] Processing task {request_id} with SMILES: {target_smiles} for iteration {itr}")
        return True
    except Exception as e:
        handle_error(e, request_id, target_smiles, molecule_name, self_task, start_time, db_ops)
        return False
    
@app.task(name='enriched_data_task')
def enriched_data_task():
    try:
        enrich_data()
    except Exception as e:
        logger.error(f"[ENRICH] Error during enrichment: {e}")
        logger.error(traceback.format_exc())
    else:
        logger.info("[ENRICH] Enrichment completed successfully.")

app.conf.beat_schedule = {
    'run-every-5minutes': {
        'task': 'enriched_data_task',
        'schedule': crontab(minute='*/5'),
    },
}

@app.task(bind=True, max_retries=3, name='process_retro_task')
def process_retro_task(self, payload):
    start_time = time.time()
    redis_client = RedisClient(app_config.REDIS_URL)
    db_ops = DbOps()
    required_fields = {"request_id", "target_smiles"}
    if not all(field in payload for field in required_fields):
        missing = required_fields - payload.keys()
        error_msg = f"Missing required fields: {missing}"
        logger.error(error_msg)
        if "request_id" in payload:
            redis_client.insert_into_output_queue({
                "request_id": payload.get("request_id"),
                "molecule_name": payload.get("molecule_name", "unknown"),
                "status": "FAILED",
                "error_message": error_msg,
                "task_id": self.request.id,
                "processing_time": time.time() - start_time
            })
        return False

    request_id = str(payload["request_id"])
    REQUEST_ID.set(request_id)  # Set the request_id in context variable
    os.environ['REQUEST_ID'] = request_id
    molecule_name = payload["molecule_name"]
    target_smiles = payload["target_smiles"]

    for env_key, payload_key in {
        'SYNTHESIS_SCORE_THRESHOLD': 'synthesis_score_threshold',
        'MIN_FORWARD_PROB': 'min_forward_prob',
        'MIN_CERTAINITY_SCORE': 'min_certainity_score',
        'HEAVY_METAL_THRESHOLD': 'heavy_metal_threshold',
        'PRUNING_FACTOR': 'pruning_factor',
        'BEAM_BASED_PRUNING': 'beam_based_pruning',
        'MAX_ROUTES': 'max_routes'
    }.items():
        if payload_key in payload:
            os.environ[env_key] = str(payload[payload_key])
    

    
    logger.info(f"[START] Processing task {request_id} with SMILES: {target_smiles}")
    threads = []
    app_config.PARALLEL_CONFIGS = json.loads(app_config.PARALLEL_CONFIGS) if isinstance(app_config.PARALLEL_CONFIGS, str) else app_config.PARALLEL_CONFIGS
    for idx, itr in enumerate(app_config.PARALLEL_CONFIGS):
        thread_request_id = f"{request_id}_{idx}"
        thread = Thread(
            target=process_single_iteration,
            args=(itr, request_id, target_smiles, molecule_name, self, start_time, db_ops)
        )
        threads.append(thread)
        thread.start()
        # Add incremental delay before starting next thread
        if idx < len(app_config.PARALLEL_CONFIGS) - 1:
            delay = 300 * (idx + 1)  # 5 minutes * thread number
            time.sleep(delay)  # Incremental delay
    for thread in threads:
        thread.join()
    logger.info(f"[END] Processing task {request_id} with SMILES: {target_smiles}")

    return True

def main():
    logger.info("[MAIN] Celery worker will pull directly from Redis broker.")


if __name__ == "__main__":
    main()
